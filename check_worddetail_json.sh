#!/bin/bash

# WordDetail目录JSON文件批量检查脚本
# 功能：检查WordDetail目录下所有JSON文件的格式和结构完整性
# 作者：AI Assistant
# 使用方法：./check_worddetail_json.sh
#
# JSON结构验证包括（严格按照 todo.md 要求）：
# - word: 单词本身（必需）
# - translation: 中文翻译（必需）
# - quotes: 引用数组（必需），每个对象包含 {english, chinese, source}
# - phrases: 短语数组（必需），每个对象包含 {english, chinese}
# - relatedWords: 相关词数组（必需），每个对象包含 {english, chinese, type}
# 注意：所有数组字段都是必需的，且必须为对象数组格式

# 检查WordDetail目录是否存在
if [ ! -d "WordDetail" ]; then
    echo "❌ 错误: 找不到 WordDetail 目录"
    exit 1
fi

# 检查是否安装了jq
if ! command -v jq > /dev/null 2>&1; then
    echo "❌ 错误: 需要安装jq工具进行JSON结构验证"
    echo "请运行: brew install jq (macOS) 或 apt install jq (Ubuntu)"
    exit 1
fi

# 获取WordDetail目录下所有JSON文件
json_files=(WordDetail/*.json)

# 检查是否有JSON文件
if [ ${#json_files[@]} -eq 0 ] || [ ! -f "${json_files[0]}" ]; then
    echo "❌ 错误: WordDetail 目录下没有找到任何JSON文件"
    exit 1
fi

echo "📝 在 WordDetail 目录中找到 ${#json_files[@]} 个JSON文件"

echo "检查JSON文件格式和结构："
echo "================================"

valid_files=()
invalid_format_files=()
structure_issues=()
quote_issues=()
content_issues=()

# 验证JSON结构的函数
validate_json_structure() {
    local file_path="$1"
    local expected_word="$2"
    local errors=()

    # 检查JSON格式是否有效
    if ! jq empty "$file_path" 2>/dev/null; then
        errors+=("JSON格式无效")
        echo "${errors[@]}"
        return 1
    fi

    # 检查必需字段是否存在（严格按照 todo.md 要求）
    local required_fields=("word" "translation" "quotes" "phrases" "relatedWords")
    for field in "${required_fields[@]}"; do
        if ! jq -e ".$field" "$file_path" > /dev/null 2>&1; then
            errors+=("缺少必需字段: $field")
        fi
    done

    # 检查word字段是否匹配
    local json_word=$(jq -r '.word' "$file_path" 2>/dev/null)
    if [[ "$json_word" != "$expected_word" ]]; then
        errors+=("word字段不匹配 (期望: $expected_word, 实际: $json_word)")
    fi

    # 检查translation字段是否为空
    local translation=$(jq -r '.translation' "$file_path" 2>/dev/null)
    if [[ -z "$translation" || "$translation" == "null" ]]; then
        errors+=("translation字段为空")
    fi

    # 检查quotes数组结构
    local quotes_count=$(jq '.quotes | length' "$file_path" 2>/dev/null)
    if [[ "$quotes_count" -eq 0 ]]; then
        errors+=("quotes数组为空")
    else
        # 检查quotes数组中每个元素的结构
        for i in $(seq 0 $((quotes_count - 1))); do
            local quote_english=$(jq -r ".quotes[$i].english" "$file_path" 2>/dev/null)
            local quote_chinese=$(jq -r ".quotes[$i].chinese" "$file_path" 2>/dev/null)
            local quote_source=$(jq -r ".quotes[$i].source" "$file_path" 2>/dev/null)

            if [[ -z "$quote_english" || "$quote_english" == "null" ]]; then
                errors+=("quotes[$i].english为空")
            fi
            if [[ -z "$quote_chinese" || "$quote_chinese" == "null" ]]; then
                errors+=("quotes[$i].chinese为空")
            fi
            if [[ -z "$quote_source" || "$quote_source" == "null" ]]; then
                errors+=("quotes[$i].source为空")
            fi
        done
    fi

    # 检查phrases数组结构 (必需字段，必须为对象数组)
    if jq -e '.phrases' "$file_path" > /dev/null 2>&1; then
        local phrases_type=$(jq -r '.phrases | type' "$file_path" 2>/dev/null)
        if [[ "$phrases_type" == "array" ]]; then
            local phrases_count=$(jq '.phrases | length' "$file_path" 2>/dev/null)
            if [[ "$phrases_count" -eq 0 ]]; then
                errors+=("phrases数组为空")
            else
                # 检查第一个元素的类型，必须为对象
                local first_phrase_type=$(jq -r '.phrases[0] | type' "$file_path" 2>/dev/null)
                if [[ "$first_phrase_type" == "object" ]]; then
                    # 检查每个对象的结构
                    for i in $(seq 0 $((phrases_count - 1))); do
                        local phrase_english=$(jq -r ".phrases[$i].english" "$file_path" 2>/dev/null)
                        local phrase_chinese=$(jq -r ".phrases[$i].chinese" "$file_path" 2>/dev/null)

                        if [[ -z "$phrase_english" || "$phrase_english" == "null" ]]; then
                            errors+=("phrases[$i].english为空")
                        fi
                        if [[ -z "$phrase_chinese" || "$phrase_chinese" == "null" ]]; then
                            errors+=("phrases[$i].chinese为空")
                        fi
                    done
                else
                    errors+=("phrases必须是对象数组，不能是字符串数组")
                fi
            fi
        else
            errors+=("phrases应该是数组类型")
        fi
    else
        errors+=("缺少必需字段: phrases")
    fi

    # 检查relatedWords数组结构 (必需字段，必须为对象数组)
    if jq -e '.relatedWords' "$file_path" > /dev/null 2>&1; then
        local related_type=$(jq -r '.relatedWords | type' "$file_path" 2>/dev/null)
        if [[ "$related_type" == "array" ]]; then
            local related_count=$(jq '.relatedWords | length' "$file_path" 2>/dev/null)
            if [[ "$related_count" -eq 0 ]]; then
                errors+=("relatedWords数组为空")
            else
                # 检查第一个元素的类型，必须为对象
                local first_related_type=$(jq -r '.relatedWords[0] | type' "$file_path" 2>/dev/null)
                if [[ "$first_related_type" == "object" ]]; then
                    # 检查每个对象的结构
                    for i in $(seq 0 $((related_count - 1))); do
                        local related_english=$(jq -r ".relatedWords[$i].english" "$file_path" 2>/dev/null)
                        local related_chinese=$(jq -r ".relatedWords[$i].chinese" "$file_path" 2>/dev/null)
                        local related_type_field=$(jq -r ".relatedWords[$i].type" "$file_path" 2>/dev/null)

                        if [[ -z "$related_english" || "$related_english" == "null" ]]; then
                            errors+=("relatedWords[$i].english为空")
                        fi
                        if [[ -z "$related_chinese" || "$related_chinese" == "null" ]]; then
                            errors+=("relatedWords[$i].chinese为空")
                        fi
                        if [[ -z "$related_type_field" || "$related_type_field" == "null" ]]; then
                            errors+=("relatedWords[$i].type为空")
                        fi
                    done
                else
                    errors+=("relatedWords必须是对象数组，不能是字符串数组")
                fi
            fi
        else
            errors+=("relatedWords应该是数组类型")
        fi
    else
        errors+=("缺少必需字段: relatedWords")
    fi

    if [[ ${#errors[@]} -gt 0 ]]; then
        echo "${errors[@]}"
        return 1
    fi

    return 0
}

# 检查quotes中是否包含目标单词的函数 (严格按照 todo.md 要求)
check_word_in_quotes() {
    local file_path="$1"
    local word="$2"

    # 创建更全面的匹配模式，包括各种词形变化
    local search_patterns=()

    # 基本形式
    search_patterns+=("\b$word\b")

    # 常见词形变化
    if [[ "$word" =~ [^aeiou]y$ ]]; then
        # 以辅音+y结尾的词，如city -> cities
        local base_word=${word%y}
        search_patterns+=("\b${base_word}ies\b")
        search_patterns+=("\b${word}s\b")
    elif [[ "$word" =~ [sxz]$ ]] || [[ "$word" =~ (ch|sh)$ ]]; then
        # 以s, x, z, ch, sh结尾的词
        search_patterns+=("\b${word}es\b")
    else
        # 一般情况
        search_patterns+=("\b${word}s\b")
    fi

    # 动词变化
    if [[ "$word" =~ e$ ]]; then
        # 以e结尾的动词
        local base_word=${word%e}
        search_patterns+=("\b${base_word}ing\b")
        search_patterns+=("\b${word}d\b")
    else
        search_patterns+=("\b${word}ing\b")
        search_patterns+=("\b${word}ed\b")
    fi

    # 副词形式
    if [[ "$word" =~ [^aeiou]y$ ]]; then
        local base_word=${word%y}
        search_patterns+=("\b${base_word}ily\b")
    else
        search_patterns+=("\b${word}ly\b")
    fi

    # 其他常见后缀
    search_patterns+=("\b${word}tion\b")
    search_patterns+=("\b${word}sion\b")
    search_patterns+=("\b${word}ness\b")
    search_patterns+=("\b${word}ment\b")

    # 组合所有模式
    local combined_pattern=$(IFS='|'; echo "${search_patterns[*]}")

    # 使用jq检查quotes中的english字段
    local quotes_english=$(jq -r '.quotes[].english' "$file_path" 2>/dev/null)

    if [[ -z "$quotes_english" ]]; then
        echo "0:0"
        return
    fi

    local quote_count=$(jq '.quotes | length' "$file_path" 2>/dev/null)
    local quotes_with_word=0

    # 逐个检查每个quote
    for i in $(seq 0 $((quote_count - 1))); do
        local quote_text=$(jq -r ".quotes[$i].english" "$file_path" 2>/dev/null)
        if echo "$quote_text" | grep -iE "$combined_pattern" > /dev/null 2>&1; then
            quotes_with_word=$((quotes_with_word + 1))
        fi
    done

    echo "$quotes_with_word:$quote_count"
}

# 从文件名提取单词的函数
extract_word_from_filename() {
    local filename="$1"
    # 移除路径和扩展名，然后移除-detail后缀
    local basename=$(basename "$filename" .json)
    echo "${basename%-detail}"
}

# 检查每个JSON文件
for file_path in "${json_files[@]}"; do
    # 从文件名提取预期的单词
    expected_word=$(extract_word_from_filename "$file_path")
    filename=$(basename "$file_path")

    # 验证JSON结构
    structure_errors=$(validate_json_structure "$file_path" "$expected_word")
    structure_check_passed=$?

    if [[ $structure_check_passed -eq 0 ]]; then
        # 检查quotes中是否包含该单词
        quote_result=$(check_word_in_quotes "$file_path" "$expected_word")
        quotes_with_word=$(echo "$quote_result" | cut -d: -f1)
        quote_count=$(echo "$quote_result" | cut -d: -f2)

        if [[ "$quotes_with_word" -eq "$quote_count" ]] && [[ "$quote_count" -gt 0 ]]; then
            echo "✓ $filename 完整且正确 (结构✓, quotes✓ $quotes_with_word/$quote_count)"
            valid_files+=("$expected_word")
        else
            echo "⚠ $filename 结构正确，但quotes中未完全包含单词 '$expected_word' (包含:$quotes_with_word/总数:$quote_count)"
            quote_issues+=("$expected_word")
        fi
    else
        echo "❌ $filename 结构有问题:"
        echo "   $structure_errors"
        structure_issues+=("$expected_word")
    fi
done

echo ""
echo "统计结果："
echo "================================"
echo "格式正确且quotes完整: ${#valid_files[@]} 个"
echo "结构问题: ${#structure_issues[@]} 个"
echo "Quotes问题: ${#quote_issues[@]} 个"
echo "总计检查: ${#json_files[@]} 个"

# 计算格式统计
correct_format_count=${#valid_files[@]}
structure_issue_count=${#structure_issues[@]}
quote_issue_count=${#quote_issues[@]}

echo ""
echo "详细统计："
echo "================================"
echo "完全符合要求: $correct_format_count 个"
echo "结构需修复: $structure_issue_count 个"
echo "Quotes需修复: $quote_issue_count 个"

# 提示信息
echo ""
if [ ${#structure_issues[@]} -eq 0 ] && [ ${#quote_issues[@]} -eq 0 ]; then
    echo "🎉 所有JSON文件格式正确，结构完整，quotes检查全部通过！"
else
    if [ ${#structure_issues[@]} -gt 0 ]; then
        echo "❌ 以下文件的JSON结构有问题，需要修正："
        for word in "${structure_issues[@]}"; do
            echo "   - ${word}-detail.json"
        done
    fi

    if [ ${#quote_issues[@]} -gt 0 ]; then
        echo "⚠️  以下文件的quotes中未包含目标单词，需要修正："
        for word in "${quote_issues[@]}"; do
            echo "   - ${word}-detail.json"
        done
    fi

    echo ""
    echo "💡 建议："
    echo "1. 优先修复结构问题的JSON文件（严格按照todo.md要求）"
    echo "2. 完善quotes内容，确保包含目标单词"
    echo "3. 验证所有字段都已正确填写且为对象数组格式"
    echo "4. 确保phrases和relatedWords都是对象数组，不能是字符串数组"
fi

echo ""
echo "🔍 检查完成！如需查看具体错误详情，请查看上方输出。"
