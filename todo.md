## 规则

- 不要使用脚本，而是你自己来生成好的内容的 JSON。生成结构严格按照下文提到的 JSON文件的结构和生成方案 来。
- 按照下面的 单词列表 ，完成所有单词的内容 JSON 生成，不需要在本文档中做完成标记。
- 完成所有单词内容生成后，再检查下有没有遗漏没有生成的，这里可以使用 check_words.sh 脚本检查。注意内容生成不能使用脚本，其它的检查可以。
- 不要调用 check_worddetail_json.sh 这个脚本。

## 单词列表

overwhelmingly
pack
pantry
part
partially
partly
patiently
peacefully
peck
peculiarly
penetrate
perfectly
permeate
personalize
pick
pierce
pluck
plunge
poke
practise
precisely
preen
preface
prefer
preferable
preferably
preference
preferential
preferentially
preform
pregnancy
pregnant
preliminary
prevent
prey
prick
printing
prod
proprietorship
prowl
pul
pull
purpose
put
quarry
quartz
railhead
raise
rarity
readership
rear
rearrange
recede
recital
reexamine
regularly
reheat
relic
remarkably
rentable
respectively
rhythmically
richly
robbery
satisfactorily
scarcely
schoolbag
scientifically
securely
sensible
setup
severely
sharply
shoeshine
shop
shortly
shuffle
sideways
signify
similarly
simply
simultaneously
singly
skeletally
skillfully
slap
slide
slightly
slit
slog
slop
slowness
sluggishly
smear
smite
snatch
sneak
snugly
soak
sociably
socially
soften
solely
solicit
solidly
soon
sort
soundly
spank
spar
spare
sparingly
spatter
spear
spearhead
specially
specifically
specify
spectacularly
spellbind
spend
spew
spill
spit
splash
spontaneously
sprawl
springtime
spur
spurn
squander
squarely
squat
squirt
stabilize
stagger
stagnate
stall
starve
stash
stave
stay
steadfastly
steer

## JSON文件的结构和生成方案。

### JSON结构设计

````json
{
  "word": "单词",
  "translation": "中文翻译",
  "quotes": [
    {
      "english": "英文名言",
      "chinese": "中文翻译",
      "source": "来源"
    },
    {
      "english": "世界名著节选",
      "chinese": "中文翻译",
      "source": "来源"
    }
  ],
  "phrases": [
    {
      "english": "英文词组",
      "chinese": "中文翻译"
    }
  ],
  "relatedWords": [
    {
      "english": "相关单词",
      "chinese": "中文翻译",
      "type": "词性或关系类型"
    }
  ]
}
````

注意：
- quotes 中的句子必须要包含当前的英语单词。

### 示例文件：good-detail.json

````json
{
  "word": "good",
  "translation": "好的，良好的",
  "quotes": [
    {
      "english": "The only thing necessary for the triumph of evil is for good men to do nothing.",
      "chinese": "邪恶获胜的唯一条件，就是善良的人什么都不做。",
      "source": "Edmund Burke"
    },
    {
      "english": "We shall never know all the good that a simple smile can do.",
      "chinese": "一个简单的微笑能带来无限善意与正能量，效果远超想象。",
      "source": "Mother Teresa"
    }
  ],
  "phrases": [
    {
      "english": "good at",
      "chinese": "擅长于"
    },
    {
      "english": "for good",
      "chinese": "永久地，一劳永逸地"
    },
    {
      "english": "good luck",
      "chinese": "好运"
    },
    {
      "english": "do good",
      "chinese": "做好事，行善"
    }
  ],
  "relatedWords": [
    {
      "english": "excellent",
      "chinese": "优秀的",
      "type": "同义词"
    },
    {
      "english": "bad",
      "chinese": "坏的",
      "type": "反义词"
    },
    {
      "english": "better",
      "chinese": "更好的",
      "type": "比较级"
    },
    {
      "english": "best",
      "chinese": "最好的",
      "type": "最高级"
    },
    {
      "english": "goodness",
      "chinese": "善良，美德",
      "type": "名词形式"
    }
  ]
}
````


这个JSON结构设计具有以下优点：
- 结构清晰，易于解析
- 支持多语言内容
- 包含了所需的所有信息类型
- 便于后续扩展和维护



